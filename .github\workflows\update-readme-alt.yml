name: Update README (Alternative)

on:
  schedule:
    # Runs every day at 00:00 UTC
    - cron: '0 0 * * *'
  workflow_dispatch:

permissions:
  contents: write

jobs:
  update-readme:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.AUTO_UPDATE_TOKEN || secrets.GITHUB_TOKEN }}
        fetch-depth: 0
        
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        npm install -g markdown-magic
        npm install axios moment
        
    - name: Update README with latest stats
      run: |
        node .github/scripts/update-readme.js
      env:
        GITHUB_TOKEN: ${{ secrets.AUTO_UPDATE_TOKEN || secrets.GITHUB_TOKEN }}
        
    - name: Commit changes
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add README.md
        git diff --staged --quiet || git commit -m "📊 Auto-update README stats [skip ci]"
        git push
      env:
        GITHUB_TOKEN: ${{ secrets.AUTO_UPDATE_TOKEN || secrets.GITHUB_TOKEN }}
