name: Update README

on:
  schedule:
    # Runs every day at 00:00 UTC
    - cron: '0 0 * * *'
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  update-readme:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        npm install -g markdown-magic
        npm install axios moment
        
    - name: Update README with latest stats
      run: |
        node .github/scripts/update-readme.js
        
    - name: Commit changes
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add README.md
        git diff --staged --quiet || git commit -m "📊 Auto-update README stats [skip ci]"
        git push
