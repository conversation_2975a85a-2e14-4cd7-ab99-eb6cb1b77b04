---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## ❌ Actual Behavior
A clear and concise description of what actually happened.

## 📸 Screenshots
If applicable, add screenshots to help explain your problem.

## 🖥️ Environment
- OS: [e.g. Windows 10, macOS 12.0, Ubuntu 20.04]
- Browser: [e.g. Chrome 96, Firefox 95, Safari 15]
- Node.js version: [e.g. 18.0.0]
- Aithor version: [e.g. 1.0.0]

## 📱 Device Information (if mobile)
- Device: [e.g. iPhone 13, Samsung Galaxy S21]
- OS: [e.g. iOS 15.0, Android 12]
- Browser: [e.g. Safari, Chrome Mobile]

## 🔍 Additional Context
Add any other context about the problem here.

## 🔗 Related Issues
Link any related issues here.

## ✅ Checklist
- [ ] I have searched for existing issues
- [ ] I have provided all required information
- [ ] I have tested this on the latest version
- [ ] I have included screenshots (if applicable)
