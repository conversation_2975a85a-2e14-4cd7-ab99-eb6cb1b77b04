{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "migrate": "node scripts/migrate-data.js", "create-admin": "node scripts/create-admin-user.js", "update-admin-password": "node scripts/create-admin-user.js --update-password", "seed-admin": "node scripts/seed-admin.js", "seed-dashboard": "node scripts/seed-dashboard-data.js", "setup": "node scripts/setup.js", "seed": "npx ts-node scripts/seed-data.ts", "seed-atlas": "node scripts/seed-atlas-production.js", "seed-production": "node scripts/seed-atlas-production.js", "db:seed-atlas": "node scripts/seed-atlas-production.js", "db:cleanup": "node scripts/cleanup-atlas.js", "db:reset-atlas": "node scripts/cleanup-atlas.js && node scripts/seed-atlas-production.js", "seed:practice": "node scripts/seed-practice-questions.js", "setup:practice": "node scripts/setup-practice-system.js", "server": "ts-node --project tsconfig.server.json server/index.ts", "readme:update": "node .github/scripts/update-readme.js", "readme:contributors": "node .github/scripts/update-contributors.js", "readme:validate": "markdown-link-check README.md", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.9.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.7.0", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "@types/react-dom": "18.2.7", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "aos": "^2.3.4", "bcryptjs": "^2.4.3", "bullmq": "^5.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "docx": "^9.5.1", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "express": "^5.1.0", "framer-motion": "^12.23.3", "http": "^0.0.1-security", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.446.0", "mammoth": "^1.9.1", "mongoose": "^8.16.2", "multer": "^2.0.1", "next": "^13.5.1", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "otplib": "^12.0.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pdf-parse": "^1.1.1", "qrcode": "^1.5.4", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.3", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.4", "redis": "^4.6.13", "remark-gfm": "^4.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.9", "zod": "^3.23.8", "zustand": "^5.0.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "22.15.17", "@types/pdf-parse": "^1.1.5", "@types/qrcode": "^1.5.5", "@types/react": "18.3.21", "@types/react-syntax-highlighter": "^15.5.13", "@types/socket.io": "^3.0.1", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "markdown-link-check": "^3.13.7", "markdown-magic": "^2.6.1", "moment": "^2.30.1", "mongodb-memory-server": "^10.1.4", "node-mocks-http": "^1.17.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "5.8.3"}}